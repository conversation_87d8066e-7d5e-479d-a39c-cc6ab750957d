import React, { useState } from 'react';

const BookingModal = ({ isOpen, onClose }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [bookingData, setBookingData] = useState({
    travelers: 1,
    checkIn: '',
    checkOut: '',
    accommodation: '',
    activities: [],
    specialRequests: ''
  });

  const packages = [
    {
      id: 1,
      name: 'Adventure Explorer',
      duration: '7 Days 6 Nights',
      price: 1299,
      image: '🏔️',
      highlights: ['Bungee Jumping', 'Skydiving', 'Hiking', 'Rafting'],
      description: 'Perfect for thrill-seekers and adventure enthusiasts'
    },
    {
      id: 2,
      name: 'Cultural Discovery',
      duration: '5 Days 4 Nights',
      price: 899,
      image: '🏛️',
      highlights: ['Maori Culture', 'Museums', 'Local Tours', 'Traditional Food'],
      description: 'Immerse yourself in New Zealand\'s rich heritage'
    },
    {
      id: 3,
      name: 'Nature & Wildlife',
      duration: '8 Days 7 Nights',
      price: 1599,
      image: '🦌',
      highlights: ['Wildlife Safari', 'National Parks', 'Bird Watching', 'Photography'],
      description: 'Discover New Zealand\'s unique flora and fauna'
    },
    {
      id: 4,
      name: 'Luxury Escape',
      duration: '10 Days 9 Nights',
      price: 2499,
      image: '✨',
      highlights: ['5-Star Hotels', 'Private Tours', 'Fine Dining', 'Spa Treatments'],
      description: 'Experience New Zealand in ultimate comfort and style'
    }
  ];

  if (!isOpen) return null;

  const handleInputChange = (e) => {
    setBookingData({
      ...bookingData,
      [e.target.name]: e.target.value
    });
  };

  const handleActivityToggle = (activity) => {
    const activities = bookingData.activities.includes(activity)
      ? bookingData.activities.filter(a => a !== activity)
      : [...bookingData.activities, activity];
    
    setBookingData({ ...bookingData, activities });
  };

  const nextStep = () => {
    if (currentStep < 3) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const handleBooking = () => {
    alert(`🎉 Booking Confirmed!\n\nPackage: ${selectedPackage.name}\nTravelers: ${bookingData.travelers}\nTotal: $${selectedPackage.price * bookingData.travelers}\n\nYou'll receive confirmation email shortly!`);
    onClose();
    setCurrentStep(1);
    setSelectedPackage(null);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-green-600 to-blue-600 p-6 rounded-t-2xl relative">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-white hover:text-gray-200 text-2xl font-bold"
          >
            ×
          </button>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span className="text-3xl">🎒</span>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-white mb-1">Book Your Adventure</h2>
              <p className="text-green-100">Create your perfect New Zealand experience</p>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="mt-6">
            <div className="flex justify-between text-white text-sm mb-2">
              <span className={currentStep >= 1 ? 'font-semibold' : ''}>Choose Package</span>
              <span className={currentStep >= 2 ? 'font-semibold' : ''}>Customize Trip</span>
              <span className={currentStep >= 3 ? 'font-semibold' : ''}>Confirm Booking</span>
            </div>
            <div className="w-full bg-white bg-opacity-20 rounded-full h-2">
              <div 
                className="bg-white h-2 rounded-full transition-all duration-500"
                style={{ width: `${(currentStep / 3) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Step 1: Choose Package */}
          {currentStep === 1 && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Choose Your Adventure Package</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {packages.map((pkg) => (
                  <div
                    key={pkg.id}
                    onClick={() => setSelectedPackage(pkg)}
                    className={`border-2 rounded-xl p-6 cursor-pointer transition-all duration-300 hover:shadow-lg ${
                      selectedPackage?.id === pkg.id
                        ? 'border-green-500 bg-green-50 shadow-lg'
                        : 'border-gray-200 hover:border-green-300'
                    }`}
                  >
                    <div className="text-center mb-4">
                      <div className="text-4xl mb-2">{pkg.image}</div>
                      <h4 className="text-lg font-bold text-gray-900">{pkg.name}</h4>
                      <p className="text-gray-600 text-sm">{pkg.duration}</p>
                    </div>
                    <div className="space-y-3">
                      <div className="text-center">
                        <span className="text-2xl font-bold text-green-600">${pkg.price}</span>
                        <span className="text-gray-500 text-sm"> per person</span>
                      </div>
                      <p className="text-gray-600 text-sm text-center">{pkg.description}</p>
                      <div className="space-y-1">
                        {pkg.highlights.map((highlight, index) => (
                          <div key={index} className="flex items-center text-sm text-gray-700">
                            <span className="text-green-500 mr-2">✓</span>
                            {highlight}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Step 2: Customize Trip */}
          {currentStep === 2 && selectedPackage && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Customize Your Trip</h3>
              
              <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <div className="flex items-center space-x-3">
                  <span className="text-2xl">{selectedPackage.image}</span>
                  <div>
                    <h4 className="font-semibold text-green-900">{selectedPackage.name}</h4>
                    <p className="text-green-700 text-sm">{selectedPackage.duration} • ${selectedPackage.price} per person</p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Number of Travelers</label>
                  <select
                    name="travelers"
                    value={bookingData.travelers}
                    onChange={handleInputChange}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  >
                    {[1,2,3,4,5,6,7,8].map(num => (
                      <option key={num} value={num}>{num} {num === 1 ? 'Traveler' : 'Travelers'}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Accommodation Preference</label>
                  <select
                    name="accommodation"
                    value={bookingData.accommodation}
                    onChange={handleInputChange}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  >
                    <option value="">Select accommodation</option>
                    <option value="budget">Budget Hotels</option>
                    <option value="mid-range">Mid-range Hotels</option>
                    <option value="luxury">Luxury Hotels</option>
                    <option value="boutique">Boutique Properties</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Check-in Date</label>
                  <input
                    type="date"
                    name="checkIn"
                    value={bookingData.checkIn}
                    onChange={handleInputChange}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Check-out Date</label>
                  <input
                    type="date"
                    name="checkOut"
                    value={bookingData.checkOut}
                    onChange={handleInputChange}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-3">Additional Activities (Optional)</label>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                  {['Wine Tasting', 'Helicopter Tour', 'Spa Treatment', 'Photography Workshop', 'Cooking Class', 'Stargazing'].map((activity) => (
                    <label key={activity} className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={bookingData.activities.includes(activity)}
                        onChange={() => handleActivityToggle(activity)}
                        className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                      />
                      <span className="text-sm text-gray-700">{activity}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Special Requests</label>
                <textarea
                  name="specialRequests"
                  value={bookingData.specialRequests}
                  onChange={handleInputChange}
                  rows="3"
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Any special requirements or preferences..."
                ></textarea>
              </div>
            </div>
          )}

          {/* Step 3: Confirm Booking */}
          {currentStep === 3 && selectedPackage && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Confirm Your Booking</h3>
              
              <div className="bg-gray-50 rounded-lg p-6 space-y-4">
                <div className="flex justify-between items-center border-b pb-4">
                  <div>
                    <h4 className="font-semibold text-gray-900">{selectedPackage.name}</h4>
                    <p className="text-gray-600">{selectedPackage.duration}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-gray-900">${selectedPackage.price} × {bookingData.travelers}</div>
                    <div className="text-2xl font-bold text-green-600">${selectedPackage.price * bookingData.travelers}</div>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div><strong>Travelers:</strong> {bookingData.travelers}</div>
                  <div><strong>Accommodation:</strong> {bookingData.accommodation || 'Standard'}</div>
                  <div><strong>Check-in:</strong> {bookingData.checkIn || 'TBD'}</div>
                  <div><strong>Check-out:</strong> {bookingData.checkOut || 'TBD'}</div>
                </div>

                {bookingData.activities.length > 0 && (
                  <div>
                    <strong className="text-sm">Additional Activities:</strong>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {bookingData.activities.map((activity, index) => (
                        <span key={index} className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                          {activity}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex items-center space-x-2">
                  <span className="text-blue-600">💳</span>
                  <div>
                    <h4 className="font-semibold text-blue-900">Payment & Booking</h4>
                    <p className="text-blue-700 text-sm">Secure payment processing. Full refund available up to 48 hours before travel.</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={prevStep}
              disabled={currentStep === 1}
              className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                currentStep === 1
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
              }`}
            >
              Previous
            </button>
            
            {currentStep < 3 ? (
              <button
                onClick={nextStep}
                disabled={currentStep === 1 && !selectedPackage}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 ${
                  currentStep === 1 && !selectedPackage
                    ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    : 'bg-green-600 text-white hover:bg-green-700'
                }`}
              >
                Next Step
              </button>
            ) : (
              <button
                onClick={handleBooking}
                className="px-8 py-3 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-all duration-200"
              >
                Confirm Booking
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingModal;
