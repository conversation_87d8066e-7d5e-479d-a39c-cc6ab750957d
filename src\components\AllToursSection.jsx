import React, { useState } from 'react';

const AllToursSection = () => {
  const [selectedTour, setSelectedTour] = useState(null);

  const tours = [
    { id: 1, name: 'Adventure Explorer', duration: '7 Days 8 Nights', rate: '₹65,000', featured: false, description: 'Thrilling adventure activities across New Zealand' },
    { id: 2, name: 'Wine & Dine Tour', duration: '5 Days 6 Nights', rate: '₹45,000', featured: false, description: 'Culinary journey through wine regions' },
    { id: 3, name: 'Premium Experience', duration: '10 Days 11 Nights', rate: '₹95,000', featured: true, description: 'Luxury tour with premium accommodations' },
    { id: 4, name: 'Nature Discovery', duration: '6 Days 7 Nights', rate: '₹55,000', featured: false, description: 'Explore pristine natural landscapes' },
    { id: 5, name: 'Cultural Journey', duration: '8 Days 9 Nights', rate: '₹70,000', featured: false, description: 'Immerse in Maori culture and traditions' },
    { id: 6, name: 'Photography Tour', duration: '9 Days 10 Nights', rate: '₹80,000', featured: false, description: 'Capture stunning landscapes with expert guides' },
    { id: 7, name: 'Family Adventure', duration: '7 Days 8 Nights', rate: '₹60,000', featured: false, description: 'Perfect family-friendly activities and attractions' },
  ];

  const handleTourClick = (tour) => {
    setSelectedTour(tour);
    // You can add more functionality here like opening a detailed view
    alert(`Selected: ${tour.name}\nDuration: ${tour.duration}\nPrice: ${tour.rate}\n\n${tour.description}`);
  };

  return (
    <div className="py-16 px-4 max-w-7xl mx-auto bg-gray-50">
      <h2 className="text-4xl font-bold text-gray-900 mb-8">
        All <span className="text-purple-600">Tours</span>
      </h2>

      {/* Tours Table */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden border-4 border-blue-400">
        {/* Table Header */}
        <div className="bg-gradient-to-r from-purple-800 to-purple-900 text-white">
          <div className="grid grid-cols-3 gap-4 px-6 py-4">
            <div className="font-bold text-lg">Tour Name</div>
            <div className="font-bold text-lg text-center">Duration</div>
            <div className="font-bold text-lg text-center">Rates</div>
          </div>
        </div>

        {/* Table Body */}
        <div className="bg-purple-50">
          {tours.map((tour, index) => (
            <div
              key={tour.id}
              onClick={() => handleTourClick(tour)}
              className={`grid grid-cols-3 gap-4 px-6 py-4 border-b border-purple-200 cursor-pointer transition-all duration-200 hover:bg-purple-100 hover:shadow-md ${
                index % 2 === 0 ? 'bg-purple-50' : 'bg-white'
              } ${selectedTour?.id === tour.id ? 'ring-2 ring-purple-400 bg-purple-100' : ''}`}
            >
              <div className="flex items-center">
                {tour.featured && (
                  <span className="text-purple-600 mr-2 text-xl animate-pulse">👑</span>
                )}
                <div>
                  <span className="font-medium text-gray-900 block">{tour.name}</span>
                  <span className="text-xs text-gray-500">{tour.description}</span>
                </div>
              </div>
              <div className="text-center text-gray-700 flex items-center justify-center">
                <div>
                  <span className="block">{tour.duration}</span>
                  <span className="text-xs text-purple-600">Click for details</span>
                </div>
              </div>
              <div className="text-center font-bold text-gray-900 flex items-center justify-center">
                <div>
                  <span className="block text-lg">{tour.rate}</span>
                  <span className="text-xs text-green-600">Best Price</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AllToursSection;
