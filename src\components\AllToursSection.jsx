import React from 'react';

const AllToursSection = () => {
  const tours = [
    { name: 'Tour ABC', duration: '7 Days 8 Nights', rate: '₹50,000', featured: false },
    { name: 'Tour ABC', duration: '7 Days 8 Nights', rate: '₹50,000', featured: false },
    { name: 'Tour ABC', duration: '7 Days 8 Nights', rate: '₹50,000', featured: true },
    { name: 'Tour ABC', duration: '7 Days 8 Nights', rate: '₹50,000', featured: false },
    { name: 'Tour ABC', duration: '7 Days 8 Nights', rate: '₹50,000', featured: false },
    { name: 'Tour ABC', duration: '7 Days 8 Nights', rate: '₹50,000', featured: false },
    { name: 'Tour ABC', duration: '7 Days 8 Nights', rate: '₹50,000', featured: false },
  ];

  return (
    <div className="py-16 px-4 max-w-7xl mx-auto bg-gray-50">
      <h2 className="text-4xl font-bold text-gray-900 mb-8">
        All <span className="text-purple-600">Tours</span>
      </h2>

      {/* Tours Table */}
      <div className="bg-white rounded-lg shadow-lg overflow-hidden border-4 border-blue-400">
        {/* Table Header */}
        <div className="bg-gradient-to-r from-purple-800 to-purple-900 text-white">
          <div className="grid grid-cols-3 gap-4 px-6 py-4">
            <div className="font-bold text-lg">Tour Name</div>
            <div className="font-bold text-lg text-center">Duration</div>
            <div className="font-bold text-lg text-center">Rates</div>
          </div>
        </div>

        {/* Table Body */}
        <div className="bg-purple-50">
          {tours.map((tour, index) => (
            <div 
              key={index} 
              className={`grid grid-cols-3 gap-4 px-6 py-4 border-b border-purple-200 ${
                index % 2 === 0 ? 'bg-purple-50' : 'bg-white'
              }`}
            >
              <div className="flex items-center">
                {tour.featured && (
                  <span className="text-purple-600 mr-2 text-xl">👑</span>
                )}
                <span className="font-medium text-gray-900">{tour.name}</span>
              </div>
              <div className="text-center text-gray-700">{tour.duration}</div>
              <div className="text-center font-bold text-gray-900">{tour.rate}</div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AllToursSection;
