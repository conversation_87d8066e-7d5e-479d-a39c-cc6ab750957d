import React from 'react';

const FAQSection = () => {
  const faqs = [
    {
      id: 1,
      question: 'How do I check order delivery status ?',
      answer: 'Please tap on "My Orders" section under main menu of App/Website/M-site to check your order status.',
      icon: '📱'
    },
    {
      id: 2,
      question: 'How do I check order delivery status ?',
      answer: 'Please tap on "My Orders" section under main menu of App/Website/M-site to check your order status.',
      icon: '📱'
    },
    {
      id: 3,
      question: 'How do I check order delivery status ?',
      answer: 'Please tap on "My Orders" section under main menu of App/Website/M-site to check your order status.',
      icon: '📱'
    },
    {
      id: 4,
      question: 'How do I check order delivery status ?',
      answer: 'Please tap on "My Orders" section under main menu of App/Website/M-site to check your order status.',
      icon: '📱'
    }
  ];

  return (
    <div className="py-16 px-4 max-w-7xl mx-auto">
      <h2 className="text-4xl font-bold text-gray-900 mb-12">
        Frequently Asked <span className="text-purple-600">Questions</span>
      </h2>

      {/* FAQ Grid */}
      <div className="border-4 border-blue-400 rounded-lg p-8 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {faqs.map((faq) => (
            <div key={faq.id} className="bg-gray-50 rounded-lg p-6">
              {/* FAQ Icon */}
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center">
                    <span className="text-white text-xl">{faq.icon}</span>
                  </div>
                </div>

                {/* FAQ Content */}
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-gray-900 mb-3">
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Explore Tours Button */}
      <div className="text-center">
        <button className="bg-purple-600 text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-purple-700 transition-colors duration-300">
          Explore Tours
        </button>
      </div>
    </div>
  );
};

export default FAQSection;
