import React, { useState } from 'react';

const FAQSection = () => {
  const [expandedFAQ, setExpandedFAQ] = useState(null);

  const toggleFAQ = (id) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  const faqs = [
    {
      id: 1,
      question: 'What documents do I need for New Zealand travel?',
      answer: 'You need a valid passport with at least 6 months validity, visa (if required), travel insurance, and proof of accommodation. Some nationalities may enter visa-free for short stays.',
      icon: '📄'
    },
    {
      id: 2,
      question: 'What is the best time to visit New Zealand?',
      answer: 'New Zealand is beautiful year-round. Summer (Dec-Feb) is peak season with warm weather. Spring (Sep-Nov) and Autumn (Mar-May) offer mild weather and fewer crowds. Winter (Jun-Aug) is perfect for skiing.',
      icon: '🌤️'
    },
    {
      id: 3,
      question: 'How do I book tours and activities?',
      answer: 'You can book tours through our website, mobile app, or by contacting our customer service. We recommend booking in advance, especially during peak season (December-February).',
      icon: '📱'
    },
    {
      id: 4,
      question: 'What should I pack for New Zealand?',
      answer: 'Pack layers for variable weather, comfortable walking shoes, rain jacket, sunscreen, and warm clothes for evenings. Don\'t forget your camera for the stunning landscapes!',
      icon: '🎒'
    }
  ];

  return (
    <div className="py-16 px-4 max-w-7xl mx-auto">
      <h2 className="text-4xl font-bold text-gray-900 mb-12">
        Frequently Asked <span className="text-purple-600">Questions</span>
      </h2>

      {/* FAQ Grid */}
      <div className="border-4 border-blue-400 rounded-lg p-8 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {faqs.map((faq) => (
            <div
              key={faq.id}
              onClick={() => toggleFAQ(faq.id)}
              className={`bg-gray-50 rounded-lg p-6 cursor-pointer transition-all duration-300 hover:shadow-lg hover:bg-gray-100 ${
                expandedFAQ === faq.id ? 'ring-2 ring-purple-400 bg-purple-50' : ''
              }`}
            >
              {/* FAQ Icon */}
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center transition-colors duration-300 ${
                    expandedFAQ === faq.id ? 'bg-purple-700' : 'bg-purple-600'
                  }`}>
                    <span className="text-white text-xl">{faq.icon}</span>
                  </div>
                </div>

                {/* FAQ Content */}
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-bold text-gray-900 mb-3">
                      {faq.question}
                    </h3>
                    <span className={`text-purple-600 text-xl transition-transform duration-300 ${
                      expandedFAQ === faq.id ? 'rotate-180' : ''
                    }`}>
                      ▼
                    </span>
                  </div>
                  <div className={`overflow-hidden transition-all duration-300 ${
                    expandedFAQ === faq.id ? 'max-h-96 opacity-100' : 'max-h-16 opacity-70'
                  }`}>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {faq.answer}
                    </p>
                    {expandedFAQ === faq.id && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <button className="text-purple-600 text-sm font-medium hover:text-purple-700">
                          Need more help? Contact us →
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Explore Tours Button */}
      <div className="text-center">
        <button
          onClick={() => alert('Redirecting to tours page...')}
          className="bg-purple-600 text-white px-8 py-3 rounded-full text-lg font-medium hover:bg-purple-700 transition-all duration-300 transform hover:scale-105 hover:shadow-lg"
        >
          Explore Tours
        </button>
      </div>
    </div>
  );
};

export default FAQSection;
