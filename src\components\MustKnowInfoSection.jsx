import React, { useState } from 'react';

const MustKnowInfoSection = () => {
  const [activeTab, setActiveTab] = useState('Important Info');

  const tabs = [
    'Important Info',
    'Documents Required',
    'Airline Guidelines'
  ];

  return (
    <div className="py-16 px-4 max-w-7xl mx-auto">
      <h2 className="text-4xl font-bold text-gray-900 mb-8">
        Must-Know <span className="text-purple-600">Information</span>
      </h2>

      {/* Tab Navigation */}
      <div className="flex space-x-4 mb-8">
        {tabs.map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`px-6 py-3 rounded-full font-medium transition-colors ${
              activeTab === tab
                ? 'bg-purple-600 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            {tab}
          </button>
        ))}
      </div>

      {/* Tab Content */}
      {activeTab === 'Important Info' && (
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="flex items-start space-x-6">
            {/* Icon */}
            <div className="flex-shrink-0">
              <div className="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-2xl">📋</span>
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 space-y-6">
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Visa Validity Period:</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>How long the visa is valid from the date of issue.</li>
                  <li>The difference between single-entry, double-entry, and multiple-entry visas.</li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Duration of Stay:</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Maximum length of stay per visit (e.g., 90 days within a 180-day period for some visas).</li>
                  <li>Rules for short stays, long stays, and temporary residence visas.</li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Permitted Activities:</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>What activities are allowed under the visa (e.g., work, study, tourism).</li>
                  <li>Restrictions on working on tourist or student visas.</li>
                </ul>
              </div>

              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Entry Ban or Restrictions:</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Certain nationalities or individuals may face restrictions, including travel bans or limited stay periods.</li>
                  <li>Conditions for transit visas and whether they allow temporary entry into the country.</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'Documents Required' && (
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="flex items-start space-x-6">
            <div className="flex-shrink-0">
              <div className="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-2xl">📄</span>
              </div>
            </div>
            <div className="flex-1 space-y-6">
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Required Documents:</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Valid passport with at least 6 months validity</li>
                  <li>Completed visa application form</li>
                  <li>Recent passport-sized photographs</li>
                  <li>Proof of accommodation bookings</li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Financial Documents:</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Bank statements for the last 3 months</li>
                  <li>Proof of employment or income</li>
                  <li>Travel insurance documentation</li>
                  <li>Return flight tickets or itinerary</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeTab === 'Airline Guidelines' && (
        <div className="bg-white rounded-lg shadow-lg p-8">
          <div className="flex items-start space-x-6">
            <div className="flex-shrink-0">
              <div className="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white text-2xl">✈️</span>
              </div>
            </div>
            <div className="flex-1 space-y-6">
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Baggage Allowance:</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Check-in baggage: 23kg for economy class</li>
                  <li>Carry-on baggage: 7kg maximum weight</li>
                  <li>Liquid restrictions: 100ml containers in clear bag</li>
                  <li>Prohibited items: Sharp objects, flammable materials</li>
                </ul>
              </div>
              <div>
                <h3 className="text-xl font-bold text-gray-900 mb-3">Check-in Requirements:</h3>
                <ul className="list-disc list-inside text-gray-700 space-y-1">
                  <li>Online check-in available 24 hours before departure</li>
                  <li>Arrive at airport 3 hours before international flights</li>
                  <li>Valid travel documents required at check-in</li>
                  <li>Special assistance requests should be made in advance</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MustKnowInfoSection;
