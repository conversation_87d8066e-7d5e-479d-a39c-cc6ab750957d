import React from 'react';

const SeasonalPopup = ({ season, isOpen, onClose }) => {
  if (!isOpen || !season) return null;

  const seasonalData = {
    'SPRING': {
      months: 'September - November',
      temperature: '10°C - 20°C',
      description: 'Spring in New Zealand brings blooming flowers, mild temperatures, and fewer crowds. Perfect for hiking, sightseeing, and experiencing the country\'s natural beauty as it awakens.',
      highlights: [
        'Blooming lupins and cherry blossoms',
        'Mild weather perfect for outdoor activities',
        'Baby animals in wildlife parks',
        'Fewer tourists, better prices'
      ],
      activities: [
        'Hiking and walking trails',
        'Garden tours and flower festivals',
        'Wildlife watching',
        'Photography tours'
      ],
      packing: [
        'Layered clothing',
        'Light rain jacket',
        'Comfortable walking shoes',
        'Sunscreen and hat'
      ],
      pros: ['Mild weather', 'Beautiful blooms', 'Lower prices', 'Less crowded'],
      cons: ['Variable weather', 'Some attractions closed', 'Shorter days']
    },
    'SUMMER': {
      months: 'December - February',
      temperature: '20°C - 30°C',
      description: 'Summer is peak season in New Zealand with warm weather, long daylight hours, and all attractions open. Perfect for beaches, outdoor adventures, and festivals.',
      highlights: [
        'Warm sunny days and long daylight',
        'All attractions and activities open',
        'Beach season and water sports',
        'Festival and event season'
      ],
      activities: [
        'Beach activities and swimming',
        'Hiking and camping',
        'Water sports and sailing',
        'Outdoor festivals and concerts'
      ],
      packing: [
        'Light summer clothing',
        'Swimwear and beach gear',
        'Strong sunscreen',
        'Light jacket for evenings'
      ],
      pros: ['Best weather', 'All activities available', 'Long days', 'Festival season'],
      cons: ['Crowded', 'Higher prices', 'Need advance booking']
    },
    'AUTUMN': {
      months: 'March - May',
      temperature: '15°C - 25°C',
      description: 'Autumn offers stunning fall colors, harvest season, and comfortable temperatures. Ideal for wine tours, scenic drives, and photography.',
      highlights: [
        'Spectacular autumn foliage',
        'Wine harvest season',
        'Comfortable temperatures',
        'Clear, crisp days'
      ],
      activities: [
        'Wine tasting and harvest tours',
        'Scenic drives and photography',
        'Hiking with fall colors',
        'Cultural festivals'
      ],
      packing: [
        'Warm layers',
        'Waterproof jacket',
        'Comfortable boots',
        'Camera for fall colors'
      ],
      pros: ['Beautiful colors', 'Wine season', 'Good weather', 'Fewer crowds'],
      cons: ['Shorter days', 'Variable weather', 'Some seasonal closures']
    },
    'WINTER': {
      months: 'June - August',
      temperature: '5°C - 15°C',
      description: 'Winter brings snow to the mountains, making it perfect for skiing and winter sports. Cozy indoor experiences and dramatic landscapes await.',
      highlights: [
        'Snow-capped mountains and skiing',
        'Cozy indoor experiences',
        'Dramatic winter landscapes',
        'Aurora viewing opportunities'
      ],
      activities: [
        'Skiing and snowboarding',
        'Hot springs and spas',
        'Indoor attractions and museums',
        'Winter festivals'
      ],
      packing: [
        'Warm winter clothing',
        'Waterproof boots',
        'Thermal layers',
        'Gloves and warm hat'
      ],
      pros: ['Skiing season', 'Cozy atmosphere', 'Lower prices', 'Unique experiences'],
      cons: ['Cold weather', 'Shorter days', 'Some roads closed', 'Limited activities']
    }
  };

  const data = seasonalData[season.name] || seasonalData['SPRING'];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-purple-800 p-6 rounded-t-2xl relative">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-white hover:text-gray-200 text-2xl font-bold"
          >
            ×
          </button>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span className="text-3xl">{season.icon}</span>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-white mb-1">{season.name}</h2>
              <p className="text-purple-100">{data.months} • {data.temperature}</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Description */}
          <div className="mb-8">
            <p className="text-gray-600 leading-relaxed text-lg">{data.description}</p>
          </div>

          {/* Two Column Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column */}
            <div className="space-y-6">
              {/* Highlights */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="mr-2">✨</span> Season Highlights
                </h3>
                <div className="space-y-2">
                  {data.highlights.map((highlight, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <span className="text-purple-500 mt-1">•</span>
                      <span className="text-gray-600">{highlight}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Activities */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="mr-2">🎯</span> Best Activities
                </h3>
                <div className="grid grid-cols-1 gap-2">
                  {data.activities.map((activity, index) => (
                    <div key={index} className="bg-purple-50 p-3 rounded-lg">
                      <span className="text-purple-700 font-medium">{activity}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-6">
              {/* Packing List */}
              <div>
                <h3 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                  <span className="mr-2">🎒</span> What to Pack
                </h3>
                <div className="space-y-2">
                  {data.packing.map((item, index) => (
                    <div key={index} className="flex items-center space-x-3">
                      <span className="text-green-500">✓</span>
                      <span className="text-gray-600">{item}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Pros and Cons */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-green-700 mb-3 flex items-center">
                    <span className="mr-2">👍</span> Pros
                  </h4>
                  <div className="space-y-1">
                    {data.pros.map((pro, index) => (
                      <div key={index} className="text-sm text-green-600">• {pro}</div>
                    ))}
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-red-700 mb-3 flex items-center">
                    <span className="mr-2">👎</span> Cons
                  </h4>
                  <div className="space-y-1">
                    {data.cons.map((con, index) => (
                      <div key={index} className="text-sm text-red-600">• {con}</div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 mt-8 pt-6 border-t border-gray-200">
            <button className="flex-1 bg-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-purple-700 transition-colors">
              Plan Your {season.name.toLowerCase()} Trip
            </button>
            <button className="flex-1 border border-purple-600 text-purple-600 py-3 px-6 rounded-lg font-medium hover:bg-purple-50 transition-colors">
              View {season.name.toLowerCase()} Tours
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SeasonalPopup;
