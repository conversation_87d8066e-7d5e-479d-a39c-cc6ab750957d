import React, { useState } from 'react';
import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
  Menu,
  MenuButton,
  MenuItem,
  MenuItems
} from '@headlessui/react';
import { Bars3Icon, BellIcon, XMarkIcon } from '@heroicons/react/24/outline';

import vigoviaLogo from '../assets/logo.PNG';

const navigation = [
  { name: 'Instant Visa', href: '#', current: false, description: 'Get your visa in 24 hours' },
  { name: 'One Week Visa', href: '#', current: false, description: 'Standard processing time' },
  { name: 'One Month Visa', href: '#', current: false, description: 'Extended stay options' },
];



function classNames(...classes) {
  return classes.filter(Boolean).join(' ');
}

export default function Example() {
  const [activeNav, setActiveNav] = useState(null);

  const handleNavClick = (item) => {
    setActiveNav(item.name);
    alert(`Navigating to ${item.name}\n${item.description}`);
  };
  return (
    <Disclosure as="nav" className="bg-[#FBF4FF]">
      <div className="mx-auto max-w-7xl px-2 sm:px-6 lg:px-8">
        <div className="relative flex h-16 items-center justify-between">
          <div className="absolute inset-y-0 left-0 flex items-center sm:hidden">
            <DisclosureButton className="group relative inline-flex items-center justify-center rounded-md p-2 text-gray-700 hover:bg-gray-200 hover:text-black focus:ring-2 focus:ring-black focus:outline-hidden focus:ring-inset">
              <span className="absolute -inset-0.5" />
              <span className="sr-only">Open main menu</span>
              <Bars3Icon aria-hidden="true" className="block size-6 group-data-open:hidden" />
              <XMarkIcon aria-hidden="true" className="hidden size-6 group-data-open:block" />
            </DisclosureButton>
          </div>
          <div className="flex flex-1 items-center justify-center sm:items-stretch sm:justify-start">
            <div className="flex shrink-0 items-center">
              <img
                alt="Vigovia Logo"
                src={vigoviaLogo}
                className="h-15 w-auto"
              />
            </div>
            <div className="hidden sm:ml-6 sm:block">
              <div className="flex space-x-4">
                {navigation.map((item) => (
                  <button
                    key={item.name}
                    onClick={() => handleNavClick(item)}
                    className={classNames(
                      activeNav === item.name
                        ? 'bg-[#541C9C] text-[#FBF4FF]'
                        : 'text-gray-700 hover:bg-gray-200 hover:text-black',
                      'rounded-md px-3 py-2 text-sm font-medium transition-all duration-200 transform hover:scale-105'
                    )}
                  >
                    {item.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 sm:static sm:inset-auto sm:ml-6 sm:pr-0 space-x-4">
            {/* Country and Language dropdowns */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700">Country</span>
              <span className="text-sm text-gray-700">Language</span>
            </div>

            {/* Login/Sign Up button */}
            <button className="bg-white text-purple-600 px-4 py-2 rounded-full text-sm font-medium hover:bg-gray-50 border border-purple-200">
              Login / Sign Up
            </button>
          </div>
        </div>
      </div>

      <DisclosurePanel className="sm:hidden">
        <div className="space-y-1 px-2 pt-2 pb-3">
          {navigation.map((item) => (
            <DisclosureButton
              key={item.name}
              as="a"
              href={item.href}
              aria-current={item.current ? 'page' : undefined}
              className={classNames(
                item.current
                  ? 'bg-[#541C9C] text-[#FBF4FF]'
                  : 'text-gray-700 hover:bg-gray-200 hover:text-black',
                'block rounded-md px-3 py-2 text-base font-medium'
              )}
            >
              {item.name}
            </DisclosureButton>
          ))}
        </div>
      </DisclosurePanel>

    </Disclosure>
  );
}
