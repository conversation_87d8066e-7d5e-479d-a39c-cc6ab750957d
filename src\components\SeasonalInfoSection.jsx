import React from 'react';

const SeasonalInfoSection = () => {
  const seasons = [
    {
      name: 'SPRING',
      icon: '🌿', // Leaf icon
      bgColor: 'bg-purple-600',
      description: 'Experience the blooming beauty of New Zealand spring'
    },
    {
      name: 'SUMMER',
      icon: '☀️', // Sun icon
      bgColor: 'bg-purple-600',
      description: 'Enjoy warm weather and outdoor adventures'
    },
    {
      name: 'AUTUMN',
      icon: '🍁', // Maple leaf icon
      bgColor: 'bg-purple-600',
      description: 'Witness stunning fall colors across the landscape'
    },
    {
      name: 'WINTER',
      icon: '❄️', // Snowflake icon
      bgColor: 'bg-purple-600',
      description: 'Discover winter sports and cozy experiences'
    }
  ];

  return (
    <div className="py-16 px-4 max-w-7xl mx-auto bg-gray-50">
      <h2 className="text-4xl font-bold text-gray-900 mb-12">
        Special <span className="text-purple-600">Seasonal Info</span>
      </h2>

      {/* Seasonal Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {seasons.map((season, index) => (
          <div 
            key={index}
            className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
          >
            {/* Icon Circle */}
            <div className="flex justify-center pt-8 pb-4">
              <div className={`w-20 h-20 ${season.bgColor} rounded-full flex items-center justify-center`}>
                <span className="text-white text-3xl">{season.icon}</span>
              </div>
            </div>

            {/* Season Name */}
            <div className="text-center pb-4">
              <h3 className="text-xl font-bold text-gray-900 mb-4">
                {season.name}
              </h3>
            </div>

            {/* Read More Button */}
            <div className="px-6 pb-8">
              <button className="w-full bg-purple-600 text-white py-3 px-6 rounded-full font-medium hover:bg-purple-700 transition-colors duration-300">
                Read More
              </button>
            </div>

            {/* Bottom border line */}
            <div className="h-1 bg-purple-600"></div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default SeasonalInfoSection;
