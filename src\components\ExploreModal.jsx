import React, { useState } from 'react';

const ExploreModal = ({ isOpen, onClose }) => {
  const [activeTab, setActiveTab] = useState('destinations');
  const [selectedDestination, setSelectedDestination] = useState(null);

  const destinations = [
    {
      id: 1,
      name: 'Auckland',
      region: 'North Island',
      image: '🏙️',
      description: 'New Zealand\'s largest city with stunning harbors and urban attractions.',
      highlights: ['Sky Tower', 'Waitemata Harbour', 'Auckland Museum', 'Viaduct Harbour'],
      bestFor: ['City Life', 'Culture', 'Dining', 'Shopping'],
      climate: 'Temperate oceanic climate with mild temperatures year-round.'
    },
    {
      id: 2,
      name: 'Queenstown',
      region: 'South Island',
      image: '🏔️',
      description: 'Adventure capital of the world surrounded by dramatic mountains.',
      highlights: ['Bungee Jumping', 'Skydiving', 'Lake Wakatipu', 'The Remarkables'],
      bestFor: ['Adventure', 'Skiing', 'Scenic Views', 'Nightlife'],
      climate: 'Continental climate with warm summers and cold winters.'
    },
    {
      id: 3,
      name: 'Rotorua',
      region: 'North Island',
      image: '🌋',
      description: 'Geothermal wonderland with rich Maori culture and natural hot springs.',
      highlights: ['Geysers', 'Hot Springs', 'Maori Culture', 'Te Puia'],
      bestFor: ['Culture', 'Relaxation', 'Nature', 'Wellness'],
      climate: 'Temperate climate with geothermal activity creating unique microclimates.'
    },
    {
      id: 4,
      name: 'Milford Sound',
      region: 'South Island',
      image: '🌊',
      description: 'Breathtaking fjord with towering waterfalls and pristine wilderness.',
      highlights: ['Mitre Peak', 'Waterfalls', 'Cruise Tours', 'Wildlife'],
      bestFor: ['Nature', 'Photography', 'Cruises', 'Hiking'],
      climate: 'High rainfall creating lush rainforest and spectacular waterfalls.'
    },
    {
      id: 5,
      name: 'Bay of Islands',
      region: 'North Island',
      image: '🏝️',
      description: 'Subtropical paradise with 144 islands and rich maritime history.',
      highlights: ['Dolphin Watching', 'Sailing', 'Historic Sites', 'Beaches'],
      bestFor: ['Water Sports', 'History', 'Relaxation', 'Wildlife'],
      climate: 'Subtropical climate with warm, humid summers and mild winters.'
    },
    {
      id: 6,
      name: 'Franz Josef Glacier',
      region: 'South Island',
      image: '🧊',
      description: 'Accessible glacier descending from the Southern Alps to near sea level.',
      highlights: ['Glacier Walks', 'Helicopter Tours', 'Ice Climbing', 'Rainforest'],
      bestFor: ['Adventure', 'Nature', 'Photography', 'Unique Experiences'],
      climate: 'Temperate rainforest climate with high precipitation.'
    }
  ];

  const activities = [
    { name: 'Bungee Jumping', icon: '🪂', difficulty: 'Extreme', duration: '2-3 hours' },
    { name: 'Skydiving', icon: '🪂', difficulty: 'Extreme', duration: '3-4 hours' },
    { name: 'White Water Rafting', icon: '🚣', difficulty: 'Moderate', duration: '4-6 hours' },
    { name: 'Hiking/Tramping', icon: '🥾', difficulty: 'Easy-Hard', duration: '2-8 hours' },
    { name: 'Wine Tasting', icon: '🍷', difficulty: 'Easy', duration: '4-6 hours' },
    { name: 'Wildlife Safari', icon: '🦌', difficulty: 'Easy', duration: '3-5 hours' },
    { name: 'Glacier Walking', icon: '🧊', difficulty: 'Moderate', duration: '4-6 hours' },
    { name: 'Sailing', icon: '⛵', difficulty: 'Easy', duration: '3-8 hours' },
    { name: 'Hot Air Ballooning', icon: '🎈', difficulty: 'Easy', duration: '3-4 hours' },
    { name: 'Scenic Flights', icon: '🚁', difficulty: 'Easy', duration: '1-3 hours' }
  ];

  const culture = [
    {
      title: 'Maori Heritage',
      icon: '🪶',
      description: 'Experience the rich culture of New Zealand\'s indigenous people through traditional performances, art, and storytelling.',
      experiences: ['Hangi Feast', 'Haka Performance', 'Maori Village Tours', 'Traditional Crafts']
    },
    {
      title: 'Local Cuisine',
      icon: '🍽️',
      description: 'Discover New Zealand\'s unique culinary scene from fresh seafood to world-renowned wines.',
      experiences: ['Green-lipped Mussels', 'Pavlova', 'Hokey Pokey Ice Cream', 'Sauvignon Blanc']
    },
    {
      title: 'Arts & Festivals',
      icon: '🎭',
      description: 'Immerse yourself in New Zealand\'s vibrant arts scene and cultural festivals throughout the year.',
      experiences: ['Wellington Arts Festival', 'Auckland Arts Festival', 'Local Galleries', 'Street Art Tours']
    }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-teal-600 p-6 rounded-t-2xl relative">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-white hover:text-gray-200 text-2xl font-bold"
          >
            ×
          </button>
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
              <span className="text-3xl">🗺️</span>
            </div>
            <div>
              <h2 className="text-3xl font-bold text-white mb-1">Explore New Zealand</h2>
              <p className="text-blue-100">Discover destinations, activities, and culture</p>
            </div>
          </div>
          
          {/* Tab Navigation */}
          <div className="mt-6">
            <div className="flex space-x-1 bg-white bg-opacity-20 rounded-lg p-1">
              {[
                { id: 'destinations', label: 'Destinations', icon: '🏞️' },
                { id: 'activities', label: 'Activities', icon: '🎯' },
                { id: 'culture', label: 'Culture', icon: '🎭' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-white text-blue-600 shadow-md'
                      : 'text-white hover:bg-white hover:bg-opacity-10'
                  }`}
                >
                  <span>{tab.icon}</span>
                  <span className="font-medium">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Destinations Tab */}
          {activeTab === 'destinations' && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-gray-900">Must-Visit Destinations</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {destinations.map((destination) => (
                  <div
                    key={destination.id}
                    onClick={() => setSelectedDestination(destination)}
                    className="border border-gray-200 rounded-xl p-6 cursor-pointer hover:shadow-lg transition-all duration-300 hover:border-blue-300"
                  >
                    <div className="text-center mb-4">
                      <div className="text-4xl mb-2">{destination.image}</div>
                      <h4 className="text-lg font-bold text-gray-900">{destination.name}</h4>
                      <p className="text-blue-600 text-sm">{destination.region}</p>
                    </div>
                    <p className="text-gray-600 text-sm mb-4">{destination.description}</p>
                    <div className="space-y-2">
                      <div className="flex flex-wrap gap-1">
                        {destination.bestFor.slice(0, 2).map((item, index) => (
                          <span key={index} className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                            {item}
                          </span>
                        ))}
                      </div>
                      <button className="w-full text-blue-600 text-sm font-medium hover:text-blue-700">
                        Learn More →
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Selected Destination Details */}
              {selectedDestination && (
                <div className="bg-blue-50 border border-blue-200 rounded-xl p-6 mt-6">
                  <div className="flex items-center space-x-4 mb-4">
                    <span className="text-3xl">{selectedDestination.image}</span>
                    <div>
                      <h4 className="text-xl font-bold text-blue-900">{selectedDestination.name}</h4>
                      <p className="text-blue-700">{selectedDestination.region}</p>
                    </div>
                  </div>
                  <p className="text-blue-800 mb-4">{selectedDestination.description}</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h5 className="font-semibold text-blue-900 mb-2">Top Highlights:</h5>
                      <ul className="space-y-1">
                        {selectedDestination.highlights.map((highlight, index) => (
                          <li key={index} className="text-blue-700 text-sm flex items-center">
                            <span className="text-blue-500 mr-2">•</span>
                            {highlight}
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h5 className="font-semibold text-blue-900 mb-2">Best For:</h5>
                      <div className="flex flex-wrap gap-2">
                        {selectedDestination.bestFor.map((item, index) => (
                          <span key={index} className="bg-blue-200 text-blue-800 text-xs px-2 py-1 rounded-full">
                            {item}
                          </span>
                        ))}
                      </div>
                      <p className="text-blue-700 text-sm mt-3">{selectedDestination.climate}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Activities Tab */}
          {activeTab === 'activities' && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-gray-900">Adventure Activities</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {activities.map((activity, index) => (
                  <div
                    key={index}
                    className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-300 hover:border-teal-300"
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <span className="text-2xl">{activity.icon}</span>
                      <div>
                        <h4 className="font-semibold text-gray-900">{activity.name}</h4>
                        <p className="text-gray-600 text-sm">{activity.duration}</p>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        activity.difficulty === 'Easy' ? 'bg-green-100 text-green-800' :
                        activity.difficulty === 'Moderate' ? 'bg-yellow-100 text-yellow-800' :
                        activity.difficulty === 'Extreme' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {activity.difficulty}
                      </span>
                      <button className="text-teal-600 text-sm font-medium hover:text-teal-700">
                        Book Now
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Culture Tab */}
          {activeTab === 'culture' && (
            <div className="space-y-6">
              <h3 className="text-xl font-semibold text-gray-900">Cultural Experiences</h3>
              <div className="space-y-6">
                {culture.map((item, index) => (
                  <div key={index} className="border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-all duration-300">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-2xl">{item.icon}</span>
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-bold text-gray-900 mb-2">{item.title}</h4>
                        <p className="text-gray-600 mb-4">{item.description}</p>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                          {item.experiences.map((exp, expIndex) => (
                            <div key={expIndex} className="bg-teal-50 text-teal-800 text-sm px-3 py-2 rounded-lg text-center">
                              {exp}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-center space-x-4 mt-8 pt-6 border-t border-gray-200">
            <button
              onClick={() => alert('Opening detailed travel guide...')}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-all duration-200"
            >
              Download Travel Guide
            </button>
            <button
              onClick={() => alert('Connecting with travel expert...')}
              className="px-6 py-3 border border-blue-600 text-blue-600 rounded-lg font-medium hover:bg-blue-50 transition-all duration-200"
            >
              Speak with Expert
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExploreModal;
