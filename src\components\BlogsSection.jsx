import React from 'react';

const BlogsSection = () => {
  const blogs = [
    {
      id: 1,
      title: 'Journeys of Discovery Uncovering Hidden Treasures',
      author: 'admin',
      comments: '05',
      excerpt: 'Aliquam eros justo, posuere lobortis viverra laoreet matti ullamcorper posuere viverra. Aliquam eros justo, posuere lobortis viverra...',
      bgColor: 'bg-gray-500'
    },
    {
      id: 2,
      title: 'Journeys of Discovery Uncovering Hidden Treasures',
      author: 'admin',
      comments: '05',
      excerpt: 'Aliquam eros justo, posuere lobortis viverra laoreet matti ullamcorper posuere viverra. Aliquam eros justo, posuere lobortis viverra...',
      bgColor: 'bg-gray-500'
    },
    {
      id: 3,
      title: 'Journeys of Discovery Uncovering Hidden Treasures',
      author: 'admin',
      comments: '05',
      excerpt: 'Aliquam eros justo, posuere lobortis viverra laoreet matti ullamcorper posuere viverra. Aliquam eros justo, posuere lobortis viverra...',
      bgColor: 'bg-gray-500'
    }
  ];

  return (
    <div className="py-16 px-4 max-w-7xl mx-auto">
      {/* Header with navigation arrows */}
      <div className="flex items-center justify-between mb-12">
        <h2 className="text-4xl font-bold text-gray-900">
          Blogs Related To <span className="text-purple-600">New Zealand</span>
        </h2>
        <div className="flex space-x-2">
          <button className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors">
            <span className="text-gray-600">‹</span>
          </button>
          <button className="w-10 h-10 rounded-full border border-gray-300 flex items-center justify-center hover:bg-gray-50 transition-colors">
            <span className="text-gray-600">›</span>
          </button>
        </div>
      </div>

      {/* Blog Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {blogs.map((blog) => (
          <div key={blog.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
            {/* Blog Image Placeholder */}
            <div className={`h-48 ${blog.bgColor} flex items-center justify-center`}>
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-lg"></div>
            </div>

            {/* Blog Content */}
            <div className="p-6">
              {/* Author and Comments */}
              <div className="flex items-center text-sm text-gray-500 mb-3">
                <div className="flex items-center mr-4">
                  <span className="mr-1">👤</span>
                  <span>By {blog.author}</span>
                </div>
                <div className="flex items-center">
                  <span className="mr-1">💬</span>
                  <span>Comments ({blog.comments})</span>
                </div>
              </div>

              {/* Blog Title */}
              <h3 className="text-lg font-bold text-gray-900 mb-3 line-clamp-2">
                {blog.title}
              </h3>

              {/* Blog Excerpt */}
              <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                {blog.excerpt}
              </p>

              {/* View Button */}
              <button className="bg-purple-600 text-white px-6 py-2 rounded-full text-sm font-medium hover:bg-purple-700 transition-colors duration-300">
                View
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default BlogsSection;
