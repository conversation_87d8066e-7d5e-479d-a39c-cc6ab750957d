import vigoviaLogo from "../assets/logo.PNG";
import { FaFacebook, FaInstagram, FaYoutube, FaLinkedin } from "react-icons/fa";

export default function Footer() {
  return (
    <footer className="bg-gradient-to-r from-[#936FE0] to-[#541C9C] text-white">
      {/* Top Links Bar */}
      <div className="hidden md:flex flex-wrap justify-center border-b border-white/30 text-sm py-4">
        {[
          "Bali Tour Packages",
          "Japan Tour Packages",
          "Vietnam Tour Packages",
          "Malaysia Tour Packages",
          "Thailand Tour Packages",
          "Europe Tour Packages",
          "Cultural Tour Packages",
          "Luxury Tour Packages",
          "Dubai Tour Packages",
          "Turkey Tour Packages",
          "Ladakh Tour Packages",
          "Singapore Tour Packages",
          "Australia Tour Packages",
          "South Korea Tour Packages",
          "Honeymoon Tour Packages",
          "Adventure Tour Packages",
        ].map((link, i) => (
          <a
            key={i}
            href="#"
            className="mx-2 mb-2 hover:underline whitespace-nowrap"
          >
            {link}
          </a>
        ))}
      </div>

      {/* Main Footer Grid */}
      <div className="max-w-7xl mx-auto px-4 py-10 grid md:grid-cols-5 gap-8 text-sm">
        <div>
          <h4 className="font-semibold mb-4">Our offerings</h4>
          <ul className="space-y-2">
            <li>Holidays</li>
            <li>Visa</li>
            <li>Forex</li>
            <li>Hotels</li>
            <li>Flights</li>
          </ul>
        </div>

        <div>
          <h4 className="font-semibold mb-4">Popular destinations</h4>
          <ul className="space-y-2">
            <li>Dubai</li>
            <li>Bali</li>
            <li>Thailand</li>
            <li>Singapore</li>
            <li>Malaysia</li>
          </ul>
        </div>

        <div>
          <h4 className="font-semibold mb-4">Vigovia Specials</h4>
          <ul className="space-y-2">
            <li>Featured experiences</li>
            <li>Group Tour</li>
            <li>Backpackers Club</li>
            <li>Offline events</li>
          </ul>
        </div>

        <div>
          <h4 className="font-semibold mb-4">Company</h4>
          <ul className="space-y-2">
            <li>About us</li>
            <li>Careers</li>
            <li>Vigovia Blog</li>
            <li>Partner Portal</li>
            <li>Accreditations</li>
          </ul>
        </div>

        <div className="space-y-3">
          <div>
            <span className="bg-white text-[#541C9C] font-bold px-3 py-1 rounded">
              Need help? Call us
            </span>
            <p className="mt-2 text-lg font-semibold">+91-8585041641</p>
          </div>
          <div>
            <p className="text-sm">Email</p>
            <p className="text-sm"><EMAIL></p>
          </div>
          <div>
            <p className="text-sm">Address</p>
            <p className="text-sm">
              HP-CDO Chethana Institute Business Park,
              North Bangalore, Karnataka India-560001
            </p>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-white/30 py-4 text-xs text-center md:text-left flex flex-col md:flex-row items-center justify-between max-w-7xl mx-auto px-4 gap-4">
        {/* Logo and Payments */}
        <div className="flex items-center gap-4">
          <img src={vigoviaLogo} alt="Vigovia" className="h-8" />
          <span>Payments:</span>
          <img
            src="https://upload.wikimedia.org/wikipedia/commons/thumb/0/04/Visa.svg/512px-Visa.svg.png"
            alt="Visa"
            className="h-4"
          />
          <img
            src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/5d/PayPal.svg/512px-PayPal.svg.png"
            alt="Paypal"
            className="h-4"
          />
        </div>

        {/* Social Icons */}
        <div className="flex gap-4 text-white">
          <FaFacebook className="hover:text-gray-300" />
          <FaInstagram className="hover:text-gray-300" />
          <FaYoutube className="hover:text-gray-300" />
          <FaLinkedin className="hover:text-gray-300" />
        </div>

        {/* Copyright */}
        <div className="text-center md:text-right text-xs text-gray-200">
          © 2025 Vigovia Travel Technologies (P) Ltd. All rights reserved. &nbsp;
          <a href="#" className="underline">
            Privacy policy
          </a>{" "}
          |{" "}
          <a href="#" className="underline">
            Legal notice
          </a>{" "}
          |{" "}
          <a href="#" className="underline">
            Accessibility
          </a>
        </div>
      </div>
    </footer>
  );
}
