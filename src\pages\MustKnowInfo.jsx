import React, { useState } from 'react';
import Navbar from '../components/Navbar';

const MustKnowInfo = () => {
  const [activeTab, setActiveTab] = useState('Important Info');

  const tabs = [
    'Important Info',
    'Documents Required',
    'Airline Guidelines'
  ];

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      
      {/* Hero Section */}
      <div className="relative h-96 bg-gradient-to-r from-purple-600 to-blue-600">
        <div 
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `url('https://images.unsplash.com/photo-1469474968028-56623f02e42e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2074&q=80')`
          }}
        >
          <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        </div>
        <div className="relative z-10 flex items-center justify-center h-full text-center text-white px-4">
          <div>
            <h1 className="text-5xl font-bold mb-4">All About New Zealand</h1>
            <p className="text-xl max-w-4xl mx-auto">
              Discover New Zealand: A Land Of Majestic Landscapes, Thrilling Adventures, And Unforgettable Experiences
            </p>
          </div>
        </div>
      </div>

      {/* About New Zealand Section */}
      <div className="py-16 px-4 max-w-7xl mx-auto">
        <h2 className="text-4xl font-bold text-gray-900 mb-12">
          About <span className="text-purple-600">New Zealand</span>
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
          {/* Left side - Image */}
          <div className="relative">
            <img
              src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
              alt="New Zealand landscape with hiker"
              className="rounded-lg shadow-lg w-full h-96 object-cover"
            />
          </div>

          {/* Right side - Content */}
          <div className="space-y-8">
            {/* Flag and Title */}
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-600 rounded-full mb-4">
                <span className="text-white text-2xl">🇳🇿</span>
              </div>
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Your Gateway to New Zealand Adventures
              </h3>
              <p className="text-gray-600 leading-relaxed">
                Welcome to New Zealand, a paradise of majestic mountains, pristine beaches, and vibrant 
                culture. From adrenaline-pumping activities to tranquil natural wonders, this land offers 
                unforgettable experiences for every traveler. Explore, indulge, and let New Zealand leave 
                you awestruck.
              </p>
            </div>

            {/* Three feature cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Food and Wine Tours */}
              <div className="text-center p-6 border border-purple-200 rounded-lg">
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-xl">📍</span>
                </div>
                <h4 className="font-bold text-gray-900 mb-2">Food and Wine Tours</h4>
                <p className="text-sm text-gray-600">
                  Embark on a culinary journey through New Zealand's renowned wine regions like Marlborough and 
                  Hawke's Bay. Indulge in farm-to-table dining experiences amidst stunning vineyards and rolling hills.
                </p>
              </div>

              {/* Adventure Activities */}
              <div className="text-center p-6 border border-purple-200 rounded-lg">
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-xl">📍</span>
                </div>
                <h4 className="font-bold text-gray-900 mb-2">Adventure Activities</h4>
                <p className="text-sm text-gray-600">
                  Feel the rush in the adventure capital of the world! Try bungee jumping in Queenstown, hike the 
                  rugged trails of Fiordland, or try white-water rafting in the untamed wilderness.
                </p>
              </div>

              {/* Nature Escapades */}
              <div className="text-center p-6 border border-purple-200 rounded-lg">
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-white text-xl">📍</span>
                </div>
                <h4 className="font-bold text-gray-900 mb-2">Nature Escapades</h4>
                <p className="text-sm text-gray-600">
                  Discover nature at its finest. Visit Fiordland National Park's majestic Milford Sound, hike through the 
                  glowworm caves, and witness ancient and serene lakes.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Must-Know Information Section */}
        <div className="mt-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-8">
            Must-Know <span className="text-purple-600">Information</span>
          </h2>

          {/* Tab Navigation */}
          <div className="flex space-x-4 mb-8">
            {tabs.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-3 rounded-full font-medium transition-colors ${
                  activeTab === tab
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          {activeTab === 'Important Info' && (
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="flex items-start space-x-6">
                {/* Icon */}
                <div className="flex-shrink-0">
                  <div className="w-16 h-16 bg-purple-600 rounded-lg flex items-center justify-center">
                    <span className="text-white text-2xl">📋</span>
                  </div>
                </div>

                {/* Content */}
                <div className="flex-1 space-y-6">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">Visa Validity Period:</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>How long the visa is valid from the date of issue.</li>
                      <li>The difference between single-entry, double-entry, and multiple-entry visas.</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">Duration of Stay:</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>Maximum length of stay per visit (e.g., 90 days within a 180-day period for some visas).</li>
                      <li>Rules for short stays, long stays, and temporary residence visas.</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">Permitted Activities:</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>What activities are allowed under the visa (e.g., work, study, tourism).</li>
                      <li>Restrictions on working on tourist or student visas.</li>
                    </ul>
                  </div>

                  <div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">Entry Ban or Restrictions:</h3>
                    <ul className="list-disc list-inside text-gray-700 space-y-1">
                      <li>Certain nationalities or individuals may face restrictions, including travel bans or limited stay periods.</li>
                      <li>Conditions for transit visas and whether they allow temporary entry into the country.</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'Documents Required' && (
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Documents Required</h3>
                <p className="text-gray-600">Content for Documents Required will be displayed here.</p>
              </div>
            </div>
          )}

          {activeTab === 'Airline Guidelines' && (
            <div className="bg-white rounded-lg shadow-lg p-8">
              <div className="text-center">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Airline Guidelines</h3>
                <p className="text-gray-600">Content for Airline Guidelines will be displayed here.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MustKnowInfo;
