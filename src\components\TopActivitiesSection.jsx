import React, { useState } from 'react';
import ActivityPopup from './ActivityPopup';

const TopActivitiesSection = () => {
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const handleActivityClick = (activity) => {
    setSelectedActivity(activity);
    setIsPopupOpen(true);
  };

  const closePopup = () => {
    setIsPopupOpen(false);
    setSelectedActivity(null);
  };
  const activities = [
    {
      name: 'Wine Tasting',
      bgColor: 'bg-gradient-to-br from-red-500 to-red-700',
      featured: true
    },
    {
      name: 'Wildlife Safari',
      bgColor: 'bg-gradient-to-br from-yellow-500 to-orange-600',
      featured: false
    },
    {
      name: 'Bungee Jumping',
      bgColor: 'bg-gradient-to-br from-blue-500 to-blue-700',
      featured: false
    },
    {
      name: 'Coromande<PERSON>',
      bgColor: 'bg-gradient-to-br from-cyan-400 to-blue-500',
      featured: false
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      bgColor: 'bg-gradient-to-br from-gray-500 to-gray-700',
      featured: false
    },
    {
      name: 'Wanaka',
      bgColor: 'bg-gradient-to-br from-blue-400 to-blue-600',
      featured: false
    },
    {
      name: 'Aoraki',
      bgColor: 'bg-gradient-to-br from-gray-400 to-gray-600',
      featured: false
    },
    {
      name: 'Lake Tekapo',
      bgColor: 'bg-gradient-to-br from-blue-300 to-blue-500',
      featured: false
    },
    {
      name: 'Rotorua',
      bgColor: 'bg-gradient-to-br from-green-600 to-green-800',
      featured: false
    },
    {
      name: 'Waitomo',
      bgColor: 'bg-gradient-to-br from-teal-500 to-teal-700',
      featured: false
    }
  ];

  return (
    <div className="py-16 px-4 max-w-7xl mx-auto">
      <h2 className="text-4xl font-bold text-gray-900 mb-12">
        Top <span className="text-purple-600">Activities And Attractions</span>
      </h2>

      {/* Activities Grid */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
        {activities.map((activity, index) => (
          <div
            key={index}
            onClick={() => handleActivityClick(activity)}
            className={`relative group cursor-pointer transform transition-all duration-300 hover:scale-105 hover:shadow-xl ${
              activity.featured ? 'ring-4 ring-blue-500 ring-offset-2' : ''
            }`}
          >
            <div className={`relative overflow-hidden rounded-3xl aspect-square ${activity.bgColor} flex items-end justify-center p-4`}>
              <div className="absolute inset-0 bg-black bg-opacity-20 rounded-3xl"></div>
              <h3 className="text-white font-semibold text-center text-sm md:text-base relative z-10 drop-shadow-lg">
                {activity.name}
              </h3>
            </div>
          </div>
        ))}
      </div>

      {/* Activity Popup */}
      <ActivityPopup
        activity={selectedActivity}
        isOpen={isPopupOpen}
        onClose={closePopup}
      />
    </div>
  );
};

export default TopActivitiesSection;
