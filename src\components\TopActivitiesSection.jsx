import React from 'react';

const TopActivitiesSection = () => {
  const activities = [
    {
      name: 'Wine Tasting',
      image: 'https://picsum.photos/400/400?random=1',
      bgColor: 'bg-red-600',
      featured: true
    },
    {
      name: 'Wildlife Safari',
      image: 'https://picsum.photos/400/400?random=2',
      bgColor: 'bg-green-600',
      featured: false
    },
    {
      name: 'Bungee Jumping',
      image: 'https://picsum.photos/400/400?random=3',
      bgColor: 'bg-blue-600',
      featured: false
    },
    {
      name: 'Coromandel',
      image: 'https://picsum.photos/400/400?random=4',
      bgColor: 'bg-cyan-600',
      featured: false
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      image: 'https://picsum.photos/400/400?random=5',
      bgColor: 'bg-gray-600',
      featured: false
    },
    {
      name: '<PERSON><PERSON>',
      image: 'https://picsum.photos/400/400?random=6',
      bgColor: 'bg-blue-500',
      featured: false
    },
    {
      name: '<PERSON><PERSON><PERSON>',
      image: 'https://picsum.photos/400/400?random=7',
      bgColor: 'bg-gray-500',
      featured: false
    },
    {
      name: 'Lake Tekapo',
      image: 'https://picsum.photos/400/400?random=8',
      bgColor: 'bg-blue-400',
      featured: false
    },
    {
      name: 'Rotorua',
      image: 'https://picsum.photos/400/400?random=9',
      bgColor: 'bg-green-700',
      featured: false
    },
    {
      name: 'Waitomo',
      image: 'https://picsum.photos/400/400?random=10',
      bgColor: 'bg-teal-600',
      featured: false
    }
  ];

  return (
    <div className="py-16 px-4 max-w-7xl mx-auto">
      <h2 className="text-4xl font-bold text-gray-900 mb-12">
        Top <span className="text-purple-600">Activities And Attractions</span>
      </h2>

      {/* Activities Grid */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-6">
        {activities.map((activity, index) => (
          <div
            key={index}
            className={`relative group cursor-pointer ${
              activity.featured ? 'border-4 border-blue-500' : ''
            }`}
          >
            <div className="relative overflow-hidden rounded-3xl aspect-square">
              {/* Fallback background color in case image doesn't load */}
              <div className={`absolute inset-0 ${activity.bgColor}`}></div>
              <img
                src={activity.image}
                alt={activity.name}
                className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110 relative z-10"
                onError={(e) => {
                  e.target.style.display = 'none';
                }}
                onLoad={(e) => {
                  e.target.style.display = 'block';
                }}
              />
              <div className="absolute inset-0 bg-black bg-opacity-40 flex items-end justify-center p-4 z-20">
                <h3 className="text-white font-semibold text-center text-sm md:text-base">
                  {activity.name}
                </h3>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default TopActivitiesSection;
