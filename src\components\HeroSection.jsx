import React from 'react';
import heroImage from '../assets/hero.jpg'; // replace with your actual image path

const HeroSection = () => {
  return (
    <section
      className="relative h-[calc(100vh-64px)] flex items-center justify-center bg-cover bg-center"
      style={{ backgroundImage: `url(${heroImage})` }}
    >
      <div className="absolute inset-0 bg-black opacity-20"></div> {/* Optional dark overlay for contrast */}

      <button className="relative z-10 bg-[#541C9C] text-[#FBF4FF] text-lg px-6 py-3 rounded-lg shadow-lg hover:bg-[#680099] transition">
        Get Started
      </button>
    </section>
  );
};

export default HeroSection;
