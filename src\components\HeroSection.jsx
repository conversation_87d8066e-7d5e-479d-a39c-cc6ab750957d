import React, { useState } from 'react';
import heroImage from '../assets/hero.jpg'; // replace with your actual image path

const HeroSection = () => {
  const [isBookingOpen, setIsBookingOpen] = useState(false);

  const handleBookNow = () => {
    setIsBookingOpen(true);
    alert('Opening booking form...\n\nFeatures:\n• Select travel dates\n• Choose tour packages\n• Customize your experience\n• Secure payment options');
  };

  const handleLearnMore = () => {
    // Smooth scroll to About section
    const aboutSection = document.querySelector('#about-section');
    if (aboutSection) {
      aboutSection.scrollIntoView({ behavior: 'smooth' });
    } else {
      alert('Learn more about New Zealand:\n\n• Stunning natural landscapes\n• Adventure activities\n• Rich Maori culture\n• World-class wine regions\n• Friendly locals');
    }
  };
  return (
    <section
      className="relative h-[calc(100vh-64px)] flex items-center justify-center bg-cover bg-center"
      style={{ backgroundImage: `url(${heroImage})` }}
    >
      <div className="absolute inset-0 bg-black opacity-40"></div>

      <div className="relative z-10 text-center text-white px-4 max-w-4xl mx-auto">
        <h1 className="text-5xl md:text-7xl font-bold mb-6 animate-fade-in">
          Discover <span className="text-purple-400">New Zealand</span>
        </h1>
        <p className="text-xl md:text-2xl mb-8 text-gray-200 animate-fade-in-delay">
          Experience breathtaking landscapes, thrilling adventures, and rich culture in the land of the long white cloud
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center animate-fade-in-delay-2">
          <button
            onClick={handleBookNow}
            className="bg-purple-600 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-purple-700 transition-all duration-300 transform hover:scale-105 hover:shadow-xl"
          >
            Book Your Adventure
          </button>
          <button
            onClick={handleLearnMore}
            className="border-2 border-white text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-white hover:text-purple-600 transition-all duration-300 transform hover:scale-105"
          >
            Explore More
          </button>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-3 gap-8 mt-16 animate-fade-in-delay-3">
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400">500+</div>
            <div className="text-sm text-gray-300">Happy Travelers</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400">50+</div>
            <div className="text-sm text-gray-300">Tour Packages</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-400">15+</div>
            <div className="text-sm text-gray-300">Years Experience</div>
          </div>
        </div>
      </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
