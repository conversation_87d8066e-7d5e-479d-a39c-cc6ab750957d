import React from 'react';

const ActivityPopup = ({ activity, isOpen, onClose }) => {
  if (!isOpen || !activity) return null;

  const activityDetails = {
    'Wine Tasting': {
      description: 'Experience New Zealand\'s world-renowned wine regions including Marlborough, Central Otago, and Hawke\'s Bay. Enjoy guided tastings, vineyard tours, and gourmet food pairings.',
      highlights: ['Marlborough Sauvignon Blanc', 'Central Otago Pinot Noir', 'Hawke\'s Bay Chardonnay', 'Vineyard Tours'],
      duration: '4-8 hours',
      price: 'From $120 NZD',
      bestTime: 'March - May (Harvest Season)',
      difficulty: 'Easy'
    },
    'Wildlife Safari': {
      description: 'Discover New Zealand\'s unique wildlife including seals, penguins, dolphins, and native birds. Perfect for nature enthusiasts and photographers.',
      highlights: ['Yellow-eyed Penguins', 'Fur Seals', 'Dolphins', 'Native Birds'],
      duration: '3-6 hours',
      price: 'From $85 NZD',
      bestTime: 'October - March',
      difficulty: 'Easy'
    },
    'Bungee Jumping': {
      description: 'Experience the ultimate adrenaline rush with bungee jumping in Queenstown, the adventure capital of the world. Jump from iconic locations with stunning views.',
      highlights: ['Kawarau Gorge Bridge', 'The Ledge', 'Nevis Swing', 'AJ Hackett Experience'],
      duration: '2-3 hours',
      price: 'From $195 NZD',
      bestTime: 'Year-round',
      difficulty: 'Extreme'
    }
  };

  const details = activityDetails[activity.name] || {
    description: `Explore the amazing ${activity.name} experience in New Zealand. Discover breathtaking landscapes and unforgettable adventures.`,
    highlights: ['Scenic Views', 'Professional Guides', 'Photo Opportunities', 'Local Culture'],
    duration: '3-5 hours',
    price: 'From $100 NZD',
    bestTime: 'Year-round',
    difficulty: 'Moderate'
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className={`${activity.bgColor} p-6 rounded-t-2xl relative`}>
          <button
            onClick={onClose}
            className="absolute top-4 right-4 text-white hover:text-gray-200 text-2xl font-bold"
          >
            ×
          </button>
          <h2 className="text-2xl font-bold text-white mb-2">{activity.name}</h2>
          <div className="flex items-center space-x-4 text-white text-sm">
            <span>⏱️ {details.duration}</span>
            <span>💰 {details.price}</span>
            <span>📅 {details.bestTime}</span>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Description */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">About This Experience</h3>
            <p className="text-gray-600 leading-relaxed">{details.description}</p>
          </div>

          {/* Highlights */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">Highlights</h3>
            <div className="grid grid-cols-2 gap-2">
              {details.highlights.map((highlight, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <span className="text-green-500">✓</span>
                  <span className="text-gray-600 text-sm">{highlight}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Details Grid */}
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-1">Duration</h4>
              <p className="text-gray-600 text-sm">{details.duration}</p>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-semibold text-gray-900 mb-1">Difficulty</h4>
              <p className="text-gray-600 text-sm">{details.difficulty}</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3">
            <button className="flex-1 bg-purple-600 text-white py-3 px-6 rounded-lg font-medium hover:bg-purple-700 transition-colors">
              Book Now
            </button>
            <button className="flex-1 border border-purple-600 text-purple-600 py-3 px-6 rounded-lg font-medium hover:bg-purple-50 transition-colors">
              Learn More
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ActivityPopup;
